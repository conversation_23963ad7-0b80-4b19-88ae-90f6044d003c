'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

const navigationItems = [
  { name: '首页', href: '/' },
  { name: '关于我们', href: '/about' },
  { name: '博客', href: '/blog' },
  { name: '联系我们', href: '/contact' },
]

export function Navigation() {
  const pathname = usePathname()

  return (
    <nav>
      <ul className="flex space-x-6">
        {navigationItems.map((item) => (
          <li key={item.href}>
            <Link
              href={item.href}
              className={cn(
                'text-white hover:text-blue-200 transition-colors',
                pathname === item.href && 'text-blue-200 font-semibold'
              )}
            >
              {item.name}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  )
}