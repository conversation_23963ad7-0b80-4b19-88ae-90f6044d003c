[{"E:\\augmentDevelop\\testContext7\\app\\about\\page.tsx": "1", "E:\\augmentDevelop\\testContext7\\app\\api\\blog\\route.ts": "2", "E:\\augmentDevelop\\testContext7\\app\\api\\blog\\[id]\\route.ts": "3", "E:\\augmentDevelop\\testContext7\\app\\api\\contact\\route.ts": "4", "E:\\augmentDevelop\\testContext7\\app\\blog\\page.tsx": "5", "E:\\augmentDevelop\\testContext7\\app\\blog\\[id]\\page.tsx": "6", "E:\\augmentDevelop\\testContext7\\app\\contact\\page.tsx": "7", "E:\\augmentDevelop\\testContext7\\app\\error.tsx": "8", "E:\\augmentDevelop\\testContext7\\app\\layout.tsx": "9", "E:\\augmentDevelop\\testContext7\\app\\loading.tsx": "10", "E:\\augmentDevelop\\testContext7\\app\\not-found.tsx": "11", "E:\\augmentDevelop\\testContext7\\app\\page.tsx": "12", "E:\\augmentDevelop\\testContext7\\src\\components\\BlogPostCard.tsx": "13", "E:\\augmentDevelop\\testContext7\\src\\components\\Navigation.tsx": "14", "E:\\augmentDevelop\\testContext7\\src\\components\\ui\\Button.tsx": "15", "E:\\augmentDevelop\\testContext7\\src\\components\\ui\\Card.tsx": "16", "E:\\augmentDevelop\\testContext7\\src\\lib\\constants.ts": "17", "E:\\augmentDevelop\\testContext7\\src\\lib\\types.ts": "18", "E:\\augmentDevelop\\testContext7\\src\\lib\\utils.ts": "19", "E:\\augmentDevelop\\testContext7\\src\\test\\setup.ts": "20", "E:\\augmentDevelop\\testContext7\\src\\__tests__\\Button.test.tsx": "21", "E:\\augmentDevelop\\testContext7\\src\\__tests__\\utils.test.ts": "22"}, {"size": 3468, "mtime": 1754381660098, "results": "23", "hashOfConfig": "24"}, {"size": 3719, "mtime": 1754617575581, "results": "25", "hashOfConfig": "24"}, {"size": 4313, "mtime": 1754617576059, "results": "26", "hashOfConfig": "24"}, {"size": 1660, "mtime": 1754617575029, "results": "27", "hashOfConfig": "24"}, {"size": 3931, "mtime": 1754381660196, "results": "28", "hashOfConfig": "24"}, {"size": 6729, "mtime": 1754381660227, "results": "29", "hashOfConfig": "24"}, {"size": 7179, "mtime": 1754381660323, "results": "30", "hashOfConfig": "24"}, {"size": 1269, "mtime": 1754381659947, "results": "31", "hashOfConfig": "24"}, {"size": 1161, "mtime": 1754620585323, "results": "32", "hashOfConfig": "24"}, {"size": 419, "mtime": 1754381659853, "results": "33", "hashOfConfig": "24"}, {"size": 1019, "mtime": 1754381660014, "results": "34", "hashOfConfig": "24"}, {"size": 2297, "mtime": 1754381659772, "results": "35", "hashOfConfig": "24"}, {"size": 1638, "mtime": 1754381660878, "results": "36", "hashOfConfig": "24"}, {"size": 894, "mtime": 1754381660608, "results": "37", "hashOfConfig": "24"}, {"size": 1778, "mtime": 1754381660401, "results": "38", "hashOfConfig": "24"}, {"size": 1958, "mtime": 1754381660514, "results": "39", "hashOfConfig": "24"}, {"size": 1122, "mtime": 1754381660960, "results": "40", "hashOfConfig": "24"}, {"size": 1041, "mtime": 1754381661010, "results": "41", "hashOfConfig": "24"}, {"size": 1346, "mtime": 1754381660933, "results": "42", "hashOfConfig": "24"}, {"size": 45, "mtime": 1754620571339, "results": "43", "hashOfConfig": "24"}, {"size": 328, "mtime": 1754620578285, "results": "44", "hashOfConfig": "24"}, {"size": 407, "mtime": 1754620574768, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "v0a522", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\augmentDevelop\\testContext7\\app\\about\\page.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\api\\blog\\route.ts", [], [], "E:\\augmentDevelop\\testContext7\\app\\api\\blog\\[id]\\route.ts", [], [], "E:\\augmentDevelop\\testContext7\\app\\api\\contact\\route.ts", [], [], "E:\\augmentDevelop\\testContext7\\app\\blog\\page.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\blog\\[id]\\page.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\contact\\page.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\error.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\layout.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\loading.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\not-found.tsx", [], [], "E:\\augmentDevelop\\testContext7\\app\\page.tsx", [], [], "E:\\augmentDevelop\\testContext7\\src\\components\\BlogPostCard.tsx", [], [], "E:\\augmentDevelop\\testContext7\\src\\components\\Navigation.tsx", [], [], "E:\\augmentDevelop\\testContext7\\src\\components\\ui\\Button.tsx", [], [], "E:\\augmentDevelop\\testContext7\\src\\components\\ui\\Card.tsx", [], [], "E:\\augmentDevelop\\testContext7\\src\\lib\\constants.ts", [], [], "E:\\augmentDevelop\\testContext7\\src\\lib\\types.ts", [], [], "E:\\augmentDevelop\\testContext7\\src\\lib\\utils.ts", [], [], "E:\\augmentDevelop\\testContext7\\src\\test\\setup.ts", [], [], "E:\\augmentDevelop\\testContext7\\src\\__tests__\\Button.test.tsx", [], [], "E:\\augmentDevelop\\testContext7\\src\\__tests__\\utils.test.ts", [], []]