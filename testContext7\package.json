{"name": "nextjs-app-router-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"next": "15.1.8", "react": "^18.3.1", "react-dom": "^18.3.1", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.57.0", "eslint-config-next": "15.1.8", "tailwindcss": "^3.4.0", "typescript": "^5.6.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}