package binarytree;

import java.util.*;

/**
 * 二叉树递归遍历专门实现
 * 专注于递归算法的教学和理解
 * 包含详细的注释和可视化输出
 */
public class RecursiveTraversal {
    
    /**
     * 前序遍历 (PreOrder): 根 -> 左 -> 右
     * 递归三部曲：
     * 1. 确定递归函数的参数和返回值
     * 2. 确定终止条件
     * 3. 确定单层递归的逻辑
     */
    public void preorderTraversal(TreeNode root) {
        preorderTraversal(root, 0);
    }
    
    private void preorderTraversal(TreeNode node, int depth) {
        // 终止条件：节点为空
        if (node == null) {
            return;
        }
        
        // 单层递归逻辑：
        // 1. 处理根节点
        printNodeWithDepth(node.val, depth, "根");
        
        // 2. 递归处理左子树
        if (node.left != null) {
            System.out.println("  ".repeat(depth + 1) + "进入左子树");
        }
        preorderTraversal(node.left, depth + 1);
        
        // 3. 递归处理右子树
        if (node.right != null) {
            System.out.println("  ".repeat(depth + 1) + "进入右子树");
        }
        preorderTraversal(node.right, depth + 1);
        
        System.out.println("  ".repeat(depth) + "节点 " + node.val + " 处理完成");
    }
    
    /**
     * 中序遍历 (InOrder): 左 -> 根 -> 右
     * 对于二叉搜索树，中序遍历可以得到升序序列
     */
    public void inorderTraversal(TreeNode root) {
        inorderTraversal(root, 0);
    }
    
    private void inorderTraversal(TreeNode node, int depth) {
        // 终止条件
        if (node == null) {
            return;
        }
        
        // 单层递归逻辑：
        // 1. 递归处理左子树
        if (node.left != null) {
            System.out.println("  ".repeat(depth) + "先处理左子树");
        }
        inorderTraversal(node.left, depth + 1);
        
        // 2. 处理根节点
        printNodeWithDepth(node.val, depth, "根");
        
        // 3. 递归处理右子树
        if (node.right != null) {
            System.out.println("  ".repeat(depth) + "再处理右子树");
        }
        inorderTraversal(node.right, depth + 1);
    }
    
    /**
     * 后序遍历 (PostOrder): 左 -> 右 -> 根
     * 适用于需要先处理子节点再处理父节点的场景（如删除节点、计算目录大小等）
     */
    public void postorderTraversal(TreeNode root) {
        postorderTraversal(root, 0);
    }
    
    private void postorderTraversal(TreeNode node, int depth) {
        // 终止条件
        if (node == null) {
            return;
        }
        
        // 单层递归逻辑：
        // 1. 递归处理左子树
        if (node.left != null) {
            System.out.println("  ".repeat(depth) + "先处理左子树");
        }
        postorderTraversal(node.left, depth + 1);
        
        // 2. 递归处理右子树
        if (node.right != null) {
            System.out.println("  ".repeat(depth) + "再处理右子树");
        }
        postorderTraversal(node.right, depth + 1);
        
        // 3. 处理根节点
        printNodeWithDepth(node.val, depth, "根");
    }
    
    /**
     * 收集遍历结果的递归实现
     */
    public List<Integer> preorderCollect(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        preorderHelper(root, result);
        return result;
    }
    
    private void preorderHelper(TreeNode node, List<Integer> result) {
        if (node == null) return;
        
        result.add(node.val);                    // 访问根
        preorderHelper(node.left, result);       // 左子树
        preorderHelper(node.right, result);      // 右子树
    }
    
    public List<Integer> inorderCollect(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        inorderHelper(root, result);
        return result;
    }
    
    private void inorderHelper(TreeNode node, List<Integer> result) {
        if (node == null) return;
        
        inorderHelper(node.left, result);        // 左子树
        result.add(node.val);                    // 访问根
        inorderHelper(node.right, result);       // 右子树
    }
    
    public List<Integer> postorderCollect(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        postorderHelper(root, result);
        return result;
    }
    
    private void postorderHelper(TreeNode node, List<Integer> result) {
        if (node == null) return;
        
        postorderHelper(node.left, result);      // 左子树
        postorderHelper(node.right, result);     // 右子树
        result.add(node.val);                    // 访问根
    }
    
    /**
     * 计算二叉树的深度（递归实现）
     */
    public int maxDepth(TreeNode root) {
        // 终止条件：空节点深度为0
        if (root == null) {
            return 0;
        }
        
        // 递归计算左右子树的深度
        int leftDepth = maxDepth(root.left);
        int rightDepth = maxDepth(root.right);
        
        // 当前节点的深度 = max(左子树深度, 右子树深度) + 1
        return Math.max(leftDepth, rightDepth) + 1;
    }
    
    /**
     * 计算二叉树的节点总数（递归实现）
     */
    public int countNodes(TreeNode root) {
        // 终止条件：空节点数量为0
        if (root == null) {
            return 0;
        }
        
        // 递归计算左右子树的节点数量
        int leftCount = countNodes(root.left);
        int rightCount = countNodes(root.right);
        
        // 当前子树的节点数 = 左子树节点数 + 右子树节点数 + 1（当前节点）
        return leftCount + rightCount + 1;
    }
    
    /**
     * 判断二叉树是否对称（递归实现）
     */
    public boolean isSymmetric(TreeNode root) {
        if (root == null) return true;
        return isSymmetricHelper(root.left, root.right);
    }
    
    private boolean isSymmetricHelper(TreeNode left, TreeNode right) {
        // 都为空，对称
        if (left == null && right == null) return true;
        
        // 一个为空，一个不为空，不对称
        if (left == null || right == null) return false;
        
        // 值不相等，不对称
        if (left.val != right.val) return false;
        
        // 递归检查：左子树的左子树 vs 右子树的右子树，左子树的右子树 vs 右子树的左子树
        return isSymmetricHelper(left.left, right.right) && 
               isSymmetricHelper(left.right, right.left);
    }
    
    /**
     * 辅助方法：带深度信息的节点打印
     */
    private void printNodeWithDepth(int val, int depth, String type) {
        String indent = "  ".repeat(depth);
        System.out.println(indent + "访问节点: " + val + " (深度: " + depth + ", 类型: " + type + ")");
    }
    
    /**
     * 创建示例二叉树
     */
    public TreeNode createSampleTree() {
        // 创建二叉树:     1
        //              /   \
        //             2     3
        //            / \   / \
        //           4   5 6   7
        TreeNode root = new TreeNode(1);
        root.left = new TreeNode(2);
        root.right = new TreeNode(3);
        root.left.left = new TreeNode(4);
        root.left.right = new TreeNode(5);
        root.right.left = new TreeNode(6);
        root.right.right = new TreeNode(7);
        
        return root;
    }
    
    /**
     * 创建对称二叉树用于测试
     */
    public TreeNode createSymmetricTree() {
        // 创建对称二叉树:  1
        //               /   \
        //              2     2
        //             / \   / \
        //            3   4 4   3
        TreeNode root = new TreeNode(1);
        root.left = new TreeNode(2);
        root.right = new TreeNode(2);
        root.left.left = new TreeNode(3);
        root.left.right = new TreeNode(4);
        root.right.left = new TreeNode(4);
        root.right.right = new TreeNode(3);
        
        return root;
    }
    
    /**
     * 可视化打印二叉树结构
     */
    public void printTree(TreeNode root) {
        System.out.println("二叉树结构:");
        printTreeHelper(root, "", true);
    }
    
    private void printTreeHelper(TreeNode node, String prefix, boolean isLast) {
        if (node == null) return;
        
        System.out.println(prefix + (isLast ? "└── " : "├── ") + node.val);
        
        if (node.left != null || node.right != null) {
            if (node.left != null) {
                printTreeHelper(node.left, prefix + (isLast ? "    " : "│   "), node.right == null);
            }
            if (node.right != null) {
                printTreeHelper(node.right, prefix + (isLast ? "    " : "│   "), true);
            }
        }
    }
    
    /**
     * 测试所有递归遍历算法
     */
    public static void main(String[] args) {
        RecursiveTraversal traversal = new RecursiveTraversal();
        TreeNode root = traversal.createSampleTree();
        
        System.out.println("=== 二叉树递归遍历详细演示 ===\n");
        
        // 打印树结构
        traversal.printTree(root);
        System.out.println();
        
        // 前序遍历演示
        System.out.println("【前序遍历 - 根左右】:");
        traversal.preorderTraversal(root);
        System.out.println("结果: " + traversal.preorderCollect(root));
        System.out.println("\n" + "─".repeat(50) + "\n");
        
        // 中序遍历演示
        System.out.println("【中序遍历 - 左根右】:");
        traversal.inorderTraversal(root);
        System.out.println("结果: " + traversal.inorderCollect(root));
        System.out.println("\n" + "─".repeat(50) + "\n");
        
        // 后序遍历演示
        System.out.println("【后序遍历 - 左右根】:");
        traversal.postorderTraversal(root);
        System.out.println("结果: " + traversal.postorderCollect(root));
        System.out.println("\n" + "─".repeat(50) + "\n");
        
        // 其他递归算法测试
        System.out.println("【其他递归算法】:");
        System.out.println("树的深度: " + traversal.maxDepth(root));
        System.out.println("节点总数: " + traversal.countNodes(root));
        System.out.println("是否对称: " + traversal.isSymmetric(root));
        
        // 测试对称树
        TreeNode symmetricRoot = traversal.createSymmetricTree();
        System.out.println("\n对称树测试:");
        traversal.printTree(symmetricRoot);
        System.out.println("是否对称: " + traversal.isSymmetric(symmetricRoot));
        
        System.out.println("\n=== 递归遍历总结 ===");
        System.out.println("1. 前序遍历：根 -> 左 -> 右，适用于复制树结构");
        System.out.println("2. 中序遍历：左 -> 根 -> 右，对BST可得到有序序列");
        System.out.println("3. 后序遍历：左 -> 右 -> 根，适用于删除树或计算大小");
        System.out.println("4. 递归三要素：参数返回值、终止条件、单层逻辑");
    }
}