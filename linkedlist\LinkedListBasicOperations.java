package linkedlist;

/**
 * 链表基本操作
 * 包含创建链表、打印链表、获取长度等基本功能
 */
public class LinkedListBasicOperations {
    
    /**
     * 从数组创建链表
     * @param arr 输入数组
     * @return 链表头节点
     */
    public ListNode createLinkedList(int[] arr) {
        if (arr == null || arr.length == 0) return null;
        
        ListNode head = new ListNode(arr[0]);
        ListNode current = head;
        
        for (int i = 1; i < arr.length; i++) {
            current.next = new ListNode(arr[i]);
            current = current.next;
        }
        
        return head;
    }
    
    /**
     * 打印链表
     * @param head 链表头节点
     */
    public void printLinkedList(ListNode head) {
        ListNode current = head;
        while (current != null) {
            System.out.print(current.val);
            if (current.next != null) {
                System.out.print(" -> ");
            }
            current = current.next;
        }
        System.out.println();
    }
    
    /**
     * 获取链表长度
     * @param head 链表头节点
     * @return 链表长度
     */
    public int getLength(ListNode head) {
        int length = 0;
        ListNode current = head;
        while (current != null) {
            length++;
            current = current.next;
        }
        return length;
    }
    
    /**
     * 在链表末尾添加节点
     * @param head 链表头节点
     * @param val 要添加的值
     * @return 新的链表头节点
     */
    public ListNode append(ListNode head, int val) {
        ListNode newNode = new ListNode(val);
        
        if (head == null) {
            return newNode;
        }
        
        ListNode current = head;
        while (current.next != null) {
            current = current.next;
        }
        current.next = newNode;
        
        return head;
    }
    
    /**
     * 在指定位置插入节点
     * @param head 链表头节点
     * @param position 插入位置（从0开始）
     * @param val 要插入的值
     * @return 新的链表头节点
     */
    public ListNode insertAt(ListNode head, int position, int val) {
        ListNode newNode = new ListNode(val);
        
        if (position == 0) {
            newNode.next = head;
            return newNode;
        }
        
        ListNode current = head;
        for (int i = 0; i < position - 1 && current != null; i++) {
            current = current.next;
        }
        
        if (current == null) {
            return head; // 位置超出链表长度
        }
        
        newNode.next = current.next;
        current.next = newNode;
        
        return head;
    }
    
    /**
     * 删除指定位置的节点
     * @param head 链表头节点
     * @param position 要删除的位置（从0开始）
     * @return 新的链表头节点
     */
    public ListNode deleteAt(ListNode head, int position) {
        if (head == null) return null;
        
        if (position == 0) {
            return head.next;
        }
        
        ListNode current = head;
        for (int i = 0; i < position - 1 && current.next != null; i++) {
            current = current.next;
        }
        
        if (current.next != null) {
            current.next = current.next.next;
        }
        
        return head;
    }
    
    /**
     * 查找指定值的节点
     * @param head 链表头节点
     * @param val 要查找的值
     * @return 找到的节点，如果没找到返回null
     */
    public ListNode find(ListNode head, int val) {
        ListNode current = head;
        while (current != null) {
            if (current.val == val) {
                return current;
            }
            current = current.next;
        }
        return null;
    }
    
    /**
     * 测试基本操作
     */
    public static void main(String[] args) {
        LinkedListBasicOperations operations = new LinkedListBasicOperations();
        
        // 测试创建链表
        System.out.println("=== 链表基本操作测试 ===");
        int[] arr = {1, 2, 3, 4, 5};
        ListNode list = operations.createLinkedList(arr);
        System.out.print("原始链表: ");
        operations.printLinkedList(list);
        System.out.println("链表长度: " + operations.getLength(list));
        
        // 测试添加节点
        list = operations.append(list, 6);
        System.out.print("添加节点后: ");
        operations.printLinkedList(list);
        
        // 测试插入节点
        list = operations.insertAt(list, 2, 10);
        System.out.print("在位置2插入10后: ");
        operations.printLinkedList(list);
        
        // 测试删除节点
        list = operations.deleteAt(list, 1);
        System.out.print("删除位置1的节点后: ");
        operations.printLinkedList(list);
        
        // 测试查找节点
        ListNode found = operations.find(list, 3);
        if (found != null) {
            System.out.println("找到值为3的节点");
        } else {
            System.out.println("未找到值为3的节点");
        }
    }
} 