import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
      <div className="mb-6">
        <h2 className="text-6xl font-bold text-gray-300 mb-4">404</h2>
        <h3 className="text-2xl font-semibold text-gray-900 mb-2">页面未找到</h3>
        <p className="text-gray-600 max-w-md">
          抱歉，您要访问的页面不存在。可能是页面地址错误，或者页面已被移除。
        </p>
      </div>
      
      <div className="space-x-4">
        <Link
          href="/"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          返回首页
        </Link>
        <button
          onClick={() => window.history.back()}
          className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
        >
          返回上页
        </button>
      </div>
    </div>
  )
}