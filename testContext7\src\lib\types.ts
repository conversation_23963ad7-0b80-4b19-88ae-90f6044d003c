/**
 * 通用类型定义
 */

export interface BlogPost {
  id: number
  title: string
  excerpt: string
  content: string
  date: string
  author: string
  category: string
  readTime: string
  tags?: string[]
  published?: boolean
}

export interface Author {
  id: number
  name: string
  email: string
  bio?: string
  avatar?: string
  socialLinks?: {
    twitter?: string
    github?: string
    linkedin?: string
  }
}

export interface ContactFormData {
  name: string
  email: string
  subject?: string
  message: string
}

export interface NavigationItem {
  name: string
  href: string
  icon?: string
  external?: boolean
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: PaginationMeta
}