package linkedlist;

/**
 * 链表合并算法
 * 包含合并两个有序链表和排序链表的算法
 */
public class LinkedListMerge {
    
    /**
     * 合并两个有序链表
     * 时间复杂度: O(n + m)
     * 空间复杂度: O(1)
     * @param l1 第一个有序链表
     * @param l2 第二个有序链表
     * @return 合并后的有序链表
     */
    public ListNode mergeTwoLists(ListNode l1, ListNode l2) {
        ListNode dummy = new ListNode(0);
        ListNode current = dummy;
        
        while (l1 != null && l2 != null) {
            if (l1.val <= l2.val) {
                current.next = l1;
                l1 = l1.next;
            } else {
                current.next = l2;
                l2 = l2.next;
            }
            current = current.next;
        }
        
        current.next = (l1 != null) ? l1 : l2;
        return dummy.next;
    }
    
    /**
     * 排序链表（归并排序）
     * 时间复杂度: O(n log n)
     * 空间复杂度: O(log n) - 递归调用栈
     * @param head 链表头节点
     * @return 排序后的链表头节点
     */
    public ListNode sortList(ListNode head) {
        if (head == null || head.next == null) return head;
        
        // 找中点
        ListNode slow = head;
        ListNode fast = head;
        ListNode prev = null;
        
        while (fast != null && fast.next != null) {
            prev = slow;
            slow = slow.next;
            fast = fast.next.next;
        }
        
        prev.next = null;
        
        // 递归排序
        ListNode left = sortList(head);
        ListNode right = sortList(slow);
        
        // 合并
        return mergeTwoLists(left, right);
    }
    
    /**
     * 合并K个有序链表
     * 使用分治法
     * 时间复杂度: O(N log k)，其中N是所有链表的总节点数，k是链表数量
     * 空间复杂度: O(log k) - 递归调用栈
     * @param lists 链表数组
     * @return 合并后的链表
     */
    public ListNode mergeKLists(ListNode[] lists) {
        if (lists == null || lists.length == 0) return null;
        return mergeKListsHelper(lists, 0, lists.length - 1);
    }
    
    /**
     * 分治法合并K个链表
     */
    private ListNode mergeKListsHelper(ListNode[] lists, int start, int end) {
        if (start == end) {
            return lists[start];
        }
        
        if (start > end) {
            return null;
        }
        
        int mid = start + (end - start) / 2;
        ListNode left = mergeKListsHelper(lists, start, mid);
        ListNode right = mergeKListsHelper(lists, mid + 1, end);
        
        return mergeTwoLists(left, right);
    }
    
    /**
     * 合并两个链表（按位置合并）
     * 将第二个链表插入到第一个链表的指定位置
     * @param l1 第一个链表
     * @param l2 第二个链表
     * @param position 插入位置（从0开始）
     * @return 合并后的链表
     */
    public ListNode mergeAtPosition(ListNode l1, ListNode l2, int position) {
        if (l2 == null) return l1;
        if (l1 == null) return l2;
        
        ListNode dummy = new ListNode(0);
        dummy.next = l1;
        ListNode current = dummy;
        
        // 移动到指定位置
        for (int i = 0; i < position && current.next != null; i++) {
            current = current.next;
        }
        
        // 保存当前位置后的部分
        ListNode temp = current.next;
        
        // 插入第二个链表
        current.next = l2;
        
        // 找到第二个链表的末尾
        while (current.next != null) {
            current = current.next;
        }
        
        // 连接原来的部分
        current.next = temp;
        
        return dummy.next;
    }
    
    /**
     * 测试合并算法
     */
    public static void main(String[] args) {
        LinkedListMerge merge = new LinkedListMerge();
        LinkedListBasicOperations operations = new LinkedListBasicOperations();
        
        // 测试合并两个有序链表
        System.out.println("=== 合并两个有序链表测试 ===");
        int[] arr1 = {1, 3, 5};
        int[] arr2 = {2, 4, 6};
        ListNode list1 = operations.createLinkedList(arr1);
        ListNode list2 = operations.createLinkedList(arr2);
        
        System.out.print("链表1: ");
        operations.printLinkedList(list1);
        System.out.print("链表2: ");
        operations.printLinkedList(list2);
        
        ListNode merged = merge.mergeTwoLists(list1, list2);
        System.out.print("合并后: ");
        operations.printLinkedList(merged);
        
        // 测试排序链表
        System.out.println("\n=== 排序链表测试 ===");
        int[] arr3 = {4, 2, 1, 3, 5};
        ListNode list3 = operations.createLinkedList(arr3);
        System.out.print("原始链表: ");
        operations.printLinkedList(list3);
        
        ListNode sorted = merge.sortList(list3);
        System.out.print("排序后: ");
        operations.printLinkedList(sorted);
        
        // 测试合并K个链表
        System.out.println("\n=== 合并K个链表测试 ===");
        int[] arr4 = {1, 4, 5};
        int[] arr5 = {1, 3, 4};
        int[] arr6 = {2, 6};
        
        ListNode list4 = operations.createLinkedList(arr4);
        ListNode list5 = operations.createLinkedList(arr5);
        ListNode list6 = operations.createLinkedList(arr6);
        
        ListNode[] lists = {list4, list5, list6};
        System.out.println("链表数组:");
        for (int i = 0; i < lists.length; i++) {
            System.out.print("链表" + (i + 1) + ": ");
            operations.printLinkedList(lists[i]);
        }
        
        ListNode kMerged = merge.mergeKLists(lists);
        System.out.print("合并K个链表后: ");
        operations.printLinkedList(kMerged);
        
        // 测试按位置合并
        System.out.println("\n=== 按位置合并测试 ===");
        int[] arr7 = {1, 2, 3, 4, 5};
        int[] arr8 = {10, 20, 30};
        ListNode list7 = operations.createLinkedList(arr7);
        ListNode list8 = operations.createLinkedList(arr8);
        
        System.out.print("链表1: ");
        operations.printLinkedList(list7);
        System.out.print("链表2: ");
        operations.printLinkedList(list8);
        
        ListNode positionMerged = merge.mergeAtPosition(list7, list8, 2);
        System.out.print("在位置2插入后: ");
        operations.printLinkedList(positionMerged);
    }
} 