import { NextRequest, NextResponse } from 'next/server'
import { ContactFormData } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json()
    
    // 验证必填字段
    if (!body.name || !body.email || !body.message) {
      return NextResponse.json(
        { success: false, error: '请填写所有必填字段' },
        { status: 400 }
      )
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: '请输入有效的邮箱地址' },
        { status: 400 }
      )
    }

    // 这里可以添加实际的邮件发送逻辑
    // 例如使用 Nodemailer、SendGrid、Resend 等服务
    console.log('收到联系表单提交:', {
      name: body.name,
      email: body.email,
      subject: body.subject || '来自网站的咨询',
      message: body.message,
      timestamp: new Date().toISOString(),
    })

    // 模拟处理延时
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json({
      success: true,
      message: '感谢您的留言！我们会尽快回复您。',
    })

  } catch (error) {
    console.error('联系表单处理错误:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误，请稍后再试' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: '联系表单 API 正常运行' },
    { status: 200 }
  )
}