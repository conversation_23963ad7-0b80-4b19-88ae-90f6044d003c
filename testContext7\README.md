# Next.js App Router 项目

这是一个基于 Next.js 15 App Router 的现代化项目结构示例，展示了如何构建高性能、可扩展的 React 应用程序。

## 🚀 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **开发工具**: <PERSON><PERSON><PERSON>, Prettier
- **运行时**: Node.js

## 📁 项目结构

```
testContext7/
├── app/                    # App Router 核心目录
│   ├── about/             # 关于页面
│   ├── blog/              # 博客相关页面
│   │   └── [id]/          # 动态路由 - 博客详情页
│   ├── contact/           # 联系页面
│   ├── api/               # API 路由
│   │   ├── blog/          # 博客 API
│   │   └── contact/       # 联系表单 API
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   ├── loading.tsx        # 全局加载组件
│   ├── error.tsx          # 全局错误组件
│   └── not-found.tsx      # 404 页面
├── src/                   # 源代码目录
│   ├── components/        # 可复用组件
│   │   ├── ui/            # UI 基础组件
│   │   │   ├── Button.tsx
│   │   │   └── Card.tsx
│   │   ├── Navigation.tsx
│   │   └── BlogPostCard.tsx
│   └── lib/               # 工具库
│       ├── utils.ts       # 通用工具函数
│       ├── constants.ts   # 常量配置
│       └── types.ts       # TypeScript 类型定义
├── public/                # 静态资源
│   ├── favicon.ico
│   └── logo.svg
├── next.config.js         # Next.js 配置
├── tailwind.config.js     # Tailwind CSS 配置
├── tsconfig.json          # TypeScript 配置
├── middleware.ts          # Next.js 中间件
└── package.json           # 项目依赖
```

## ✨ 主要特性

### 🔥 Next.js App Router
- 服务端组件 (Server Components)
- 嵌套布局 (Nested Layouts)
- 加载状态 (Loading States)
- 错误边界 (Error Boundaries)
- 路由拦截 (Route Interception)

### 🎨 现代化 UI
- Tailwind CSS 响应式设计
- 自定义 UI 组件库
- 统一的设计系统
- 优化的用户体验

### 🛠️ 开发体验
- TypeScript 类型安全
- ESLint 代码质量检查
- 自动代码格式化
- 模块化架构

### 🚀 性能优化
- 自动代码分割
- 静态生成 (SSG)
- 服务端渲染 (SSR)
- 图片优化

## 🎯 页面功能

### 首页 (`/`)
- 项目介绍和特性展示
- 导航到其他页面的快捷入口
- 响应式设计

### 关于页面 (`/about`)
- 团队介绍和使命
- 技术栈展示
- 价值观和理念

### 博客 (`/blog`)
- 技术文章列表
- 分类和搜索功能
- 分页支持
- 个人博客详情页 (`/blog/[id]`)

### 联系页面 (`/contact`)
- 联系表单
- 联系信息展示
- 工作时间说明

## 🔧 开发指南

### 安装依赖
```bash
npm install
# 或者
yarn install
# 或者
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或者
yarn dev
# 或者
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
npm start
```

### 代码检查
```bash
npm run lint
```

## 📚 API 路由

### 博客 API
- `GET /api/blog` - 获取博客文章列表
- `POST /api/blog` - 创建新博客文章
- `GET /api/blog/[id]` - 获取指定博客文章
- `PUT /api/blog/[id]` - 更新博客文章
- `DELETE /api/blog/[id]` - 删除博客文章

### 联系表单 API
- `POST /api/contact` - 处理联系表单提交

## 🎨 自定义组件

### UI 组件
- `Button` - 可定制的按钮组件
- `Card` - 卡片容器组件
- `Navigation` - 导航栏组件
- `BlogPostCard` - 博客文章卡片

### 工具函数
- `cn()` - Tailwind CSS 类名合并
- `formatDate()` - 日期格式化
- `truncateText()` - 文本截断
- `isValidEmail()` - 邮箱验证

## 🔧 配置说明

### TypeScript 配置
- 严格模式启用
- 路径别名支持 (`@/`)
- Next.js 插件集成

### Tailwind CSS 配置
- 自定义颜色系统
- 响应式断点
- 动画效果
- Typography 插件

### Next.js 配置
- 实验性功能配置
- 构建优化设置

## 📝 开发建议

1. **文件组织**: 按功能模块组织代码，保持清晰的目录结构
2. **组件设计**: 优先使用服务端组件，必要时使用客户端组件
3. **类型安全**: 充分利用 TypeScript 的类型检查功能
4. **性能优化**: 使用 Next.js 的内置优化功能
5. **代码规范**: 遵循 ESLint 规则，保持代码质量

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

> 这个项目展示了 Next.js App Router 的最佳实践和现代化开发模式。如果您有任何问题或建议，请随时联系我们。