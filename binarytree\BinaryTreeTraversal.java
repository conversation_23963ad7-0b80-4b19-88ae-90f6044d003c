package binarytree;

import java.util.*;

/**
 * 二叉树遍历算法
 * 包含前序、中序、后序和层序遍历
 */
public class BinaryTreeTraversal {
    
    /**
     * 前序遍历：根 -> 左 -> 右
     * 递归实现
     */
    public List<Integer> preorderTraversal(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        preorderHelper(root, result);
        return result;
    }

    private void preorderHelper(TreeNode node, List<Integer> result) {
        if (node == null) return;
        result.add(node.val);           // 访问根节点
        preorderHelper(node.left, result);   // 遍历左子树
        preorderHelper(node.right, result);  // 遍历右子树
    }
    
    /**
     * 前序遍历：迭代实现
     */
    public List<Integer> preorderTraversalIterative(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        if (root == null) return result;
        
        Stack<TreeNode> stack = new Stack<>();
        stack.push(root);
        
        while (!stack.isEmpty()) {
            TreeNode node = stack.pop();
            result.add(node.val);
            
            // 先压入右子节点，再压入左子节点（栈的特性）
            if (node.right != null) {
                stack.push(node.right);
            }
            if (node.left != null) {
                stack.push(node.left);
            }
        }
        
        return result;
    }

    /**
     * 中序遍历：左 -> 根 -> 右
     * 递归实现
     */
    public List<Integer> inorderTraversal(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        inorderHelper(root, result);
        return result;
    }
    
    private void inorderHelper(TreeNode node, List<Integer> result) {
        if (node == null) return;
        inorderHelper(node.left, result);   // 遍历左子树
        result.add(node.val);               // 访问根节点
        inorderHelper(node.right, result);  // 遍历右子树
    }
    
    /**
     * 中序遍历：迭代实现
     */
    public List<Integer> inorderTraversalIterative(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        Stack<TreeNode> stack = new Stack<>();
        TreeNode current = root;
        
        while (current != null || !stack.isEmpty()) {
            while (current != null) {
                stack.push(current);
                current = current.left;
            }
            current = stack.pop();
            result.add(current.val);
            current = current.right;
        }
        return result;
    }

    /**
     * 后序遍历：左 -> 右 -> 根
     * 递归实现
     */
    public List<Integer> postorderTraversal(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        postorderHelper(root, result);
        return result;
    }
    
    private void postorderHelper(TreeNode node, List<Integer> result) {
        if (node == null) return;
        postorderHelper(node.left, result);   // 遍历左子树
        postorderHelper(node.right, result);  // 遍历右子树
        result.add(node.val);                 // 访问根节点
    }
    
    /**
     * 后序遍历：迭代实现
     */
    public List<Integer> postorderTraversalIterative(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        if (root == null) return result;
        
        Stack<TreeNode> stack = new Stack<>();
        TreeNode lastVisited = null;
        TreeNode current = root;
        
        while (current != null || !stack.isEmpty()) {
            if (current != null) {
                stack.push(current);
                current = current.left;
            } else {
                TreeNode peekNode = stack.peek();
                if (peekNode.right != null && lastVisited != peekNode.right) {
                    current = peekNode.right;
                } else {
                    result.add(peekNode.val);
                    lastVisited = stack.pop();
                }
            }
        }
        return result;
    }
    
    /**
     * 层序遍历 - 返回结果列表
     */
    public List<Integer> levelOrder(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        if (root == null) return result;
        
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        
        while (!queue.isEmpty()) {
            TreeNode node = queue.poll();
            result.add(node.val);
            
            if (node.left != null) queue.offer(node.left);
            if (node.right != null) queue.offer(node.right);
        }
        
        return result;
    }
    
    /**
     * 层序遍历 - 按层分组
     */
    public List<List<Integer>> levelOrderByLevel(TreeNode root) {
        List<List<Integer>> result = new ArrayList<>();
        if (root == null) return result;
        
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        
        while (!queue.isEmpty()) {
            int levelSize = queue.size();
            List<Integer> currentLevel = new ArrayList<>();
            
            for (int i = 0; i < levelSize; i++) {
                TreeNode node = queue.poll();
                currentLevel.add(node.val);
                
                if (node.left != null) queue.offer(node.left);
                if (node.right != null) queue.offer(node.right);
            }
            
            result.add(currentLevel);
        }
        
        return result;
    }
    
    /**
     * 锯齿形层序遍历
     */
    public List<List<Integer>> zigzagLevelOrder(TreeNode root) {
        List<List<Integer>> result = new ArrayList<>();
        if (root == null) return result;
        
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        boolean leftToRight = true;
        
        while (!queue.isEmpty()) {
            int levelSize = queue.size();
            List<Integer> currentLevel = new ArrayList<>();
            
            for (int i = 0; i < levelSize; i++) {
                TreeNode node = queue.poll();
                
                if (leftToRight) {
                    currentLevel.add(node.val);
                } else {
                    currentLevel.add(0, node.val);
                }
                
                if (node.left != null) queue.offer(node.left);
                if (node.right != null) queue.offer(node.right);
            }
            
            result.add(currentLevel);
            leftToRight = !leftToRight;
        }
        
        return result;
    }
    
    /**
     * 创建示例二叉树
     */
    public TreeNode createSampleTree() {
        // 创建二叉树:     1
        //              /   \
        //             2     3
        //            / \   / \
        //           4   5 6   7
        TreeNode root = new TreeNode(1);
        root.left = new TreeNode(2);
        root.right = new TreeNode(3);
        root.left.left = new TreeNode(4);
        root.left.right = new TreeNode(5);
        root.right.left = new TreeNode(6);
        root.right.right = new TreeNode(7);
        
        return root;
    }
    
    /**
     * 测试遍历算法
     */
    public static void main(String[] args) {
        BinaryTreeTraversal traversal = new BinaryTreeTraversal();
        TreeNode root = traversal.createSampleTree();
        
        System.out.println("=== 二叉树遍历测试 ===");
        
        // 测试前序遍历
        System.out.println("前序遍历 (递归): " + traversal.preorderTraversal(root));
        System.out.println("前序遍历 (迭代): " + traversal.preorderTraversalIterative(root));
        
        // 测试中序遍历
        System.out.println("中序遍历 (递归): " + traversal.inorderTraversal(root));
        System.out.println("中序遍历 (迭代): " + traversal.inorderTraversalIterative(root));
        
        // 测试后序遍历
        System.out.println("后序遍历 (递归): " + traversal.postorderTraversal(root));
        System.out.println("后序遍历 (迭代): " + traversal.postorderTraversalIterative(root));
        
        // 测试层序遍历
        System.out.println("层序遍历: " + traversal.levelOrder(root));
        System.out.println("层序分组: " + traversal.levelOrderByLevel(root));
        System.out.println("锯齿形层序: " + traversal.zigzagLevelOrder(root));
    }
} 