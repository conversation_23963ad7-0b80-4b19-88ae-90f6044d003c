package array;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

/**
 * 十大排序算法实现
 * 包含冒泡、选择、插入、归并、快速、堆、计数、基数、桶、希尔排序
 */
public class SortingAlgorithms {

    // 1. 冒泡排序 (Bubble Sort)
    public void bubbleSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - 1 - i; j++) {
                if (arr[j] > arr[j + 1]) {
                    // 交换 arr[j] 和 arr[j+1]
                    int temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }

    // 2. 选择排序 (Selection Sort)
    public void selectionSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            int minIndex = i;
            for (int j = i + 1; j < n; j++) {
                if (arr[j] < arr[minIndex]) {
                    minIndex = j;
                }
            }
            // 将找到的最小元素与 arr[i] 交换
            int temp = arr[minIndex];
            arr[minIndex] = arr[i];
            arr[i] = temp;
        }
    }

    // 3. 插入排序 (Insertion Sort)
    public void insertionSort(int[] arr) {
        int n = arr.length;
        for (int i = 1; i < n; i++) {
            int key = arr[i];
            int j = i - 1;
            // 将 arr[i] 插入到已排序的子数组中
            while (j >= 0 && arr[j] > key) {
                arr[j + 1] = arr[j];
                j--;
            }
            arr[j + 1] = key;
        }
    }

    // 4. 归并排序 (Merge Sort)
    public void mergeSort(int[] arr) {
        if (arr == null || arr.length < 2) {
            return;
        }
        mergeSort(arr, 0, arr.length - 1);
    }

    private void mergeSort(int[] arr, int left, int right) {
        if (left < right) {
            int mid = left + (right - left) / 2;
            mergeSort(arr, left, mid);
            mergeSort(arr, mid + 1, right);
            merge(arr, left, mid, right);
        }
    }

    private void merge(int[] arr, int left, int mid, int right) {
        int[] temp = new int[right - left + 1];
        int i = left;
        int j = mid + 1;
        int k = 0;

        while (i <= mid && j <= right) {
            if (arr[i] <= arr[j]) {
                temp[k++] = arr[i++];
            } else {
                temp[k++] = arr[j++];
            }
        }

        while (i <= mid) {
            temp[k++] = arr[i++];
        }

        while (j <= right) {
            temp[k++] = arr[j++];
        }

        for (int l = 0; l < temp.length; l++) {
            arr[left + l] = temp[l];
        }
    }

    // 5. 快速排序 (Quick Sort)
    public void quickSort(int[] arr) {
        if (arr == null || arr.length < 2) {
            return;
        }
        quickSort(arr, 0, arr.length - 1);
    }

    private void quickSort(int[] arr, int low, int high) {
        if (low < high) {
            int pi = partition(arr, low, high);
            quickSort(arr, low, pi - 1);
            quickSort(arr, pi + 1, high);
        }
    }

    private int partition(int[] arr, int low, int high) {
        int pivot = arr[high];
        int i = (low - 1);
        for (int j = low; j < high; j++) {
            if (arr[j] < pivot) {
                i++;
                // 交换 arr[i] 和 arr[j]
                int temp = arr[i];
                arr[i] = arr[j];
                arr[j] = temp;
            }
        }
        // 交换 arr[i+1] 和 arr[high] (pivot)
        int temp = arr[i + 1];
        arr[i + 1] = arr[high];
        arr[high] = temp;

        return i + 1;
    }

    // 6. 堆排序 (Heap Sort)
    public void heapSort(int[] arr) {
        int n = arr.length;

        // 构建最大堆 (从最后一个非叶子节点开始)
        for (int i = n / 2 - 1; i >= 0; i--) {
            heapify(arr, n, i);
        }

        // 堆排序
        for (int i = n - 1; i > 0; i--) {
            // 将当前根节点 (最大元素) 与数组末尾元素交换
            int temp = arr[0];
            arr[0] = arr[i];
            arr[i] = temp;

            // 对剩余堆进行堆化
            heapify(arr, i, 0);
        }
    }

    private void heapify(int[] arr, int n, int i) {
        int largest = i; // 初始化最大元素为根节点
        int left = 2 * i + 1; // 左子节点
        int right = 2 * i + 2; // 右子节点

        // 如果左子节点大于根节点
        if (left < n && arr[left] > arr[largest]) {
            largest = left;
        }

        // 如果右子节点大于目前的最大节点
        if (right < n && arr[right] > arr[largest]) {
            largest = right;
        }

        // 如果最大元素不是根节点
        if (largest != i) {
            int swap = arr[i];
            arr[i] = arr[largest];
            arr[largest] = swap;

            // 递归地堆化受影响的子树
            heapify(arr, n, largest);
        }
    }

    // 7. 计数排序 (Counting Sort)
    // 适用于非负整数，且范围不大的情况
    public void countingSort(int[] arr) {
        int n = arr.length;
        if (n == 0) return;

        int max = arr[0];
        for (int i = 1; i < n; i++) {
            if (arr[i] > max) {
                max = arr[i];
            }
        }

        int[] count = new int[max + 1];
        for (int num : arr) {
            count[num]++;
        }

        for (int i = 1; i < count.length; i++) {
            count[i] += count[i - 1];
        }

        int[] output = new int[n];
        for (int i = n - 1; i >= 0; i--) {
            output[count[arr[i]] - 1] = arr[i];
            count[arr[i]]--;
        }

        for (int i = 0; i < n; i++) {
            arr[i] = output[i];
        }
    }

    // 8. 基数排序 (Radix Sort)
    // 适用于非负整数
    public void radixSort(int[] arr) {
        int n = arr.length;
        if (n == 0) return;

        int max = arr[0];
        for (int i = 1; i < n; i++) {
            if (arr[i] > max) {
                max = arr[i];
            }
        }

        // 对每一位进行计数排序
        for (int exp = 1; max / exp > 0; exp *= 10) {
            countSortForRadix(arr, exp);
        }
    }

    private void countSortForRadix(int[] arr, int exp) {
        int n = arr.length;
        int[] output = new int[n];
        int[] count = new int[10]; // 0-9 for digits

        // 统计当前位每个数字出现的次数
        for (int i = 0; i < n; i++) {
            count[(arr[i] / exp) % 10]++;
        }

        // 累加计数数组
        for (int i = 1; i < 10; i++) {
            count[i] += count[i - 1];
        }

        // 放置元素到输出数组
        for (int i = n - 1; i >= 0; i--) {
            output[count[(arr[i] / exp) % 10] - 1] = arr[i];
            count[(arr[i] / exp) % 10]--;
        }

        // 将输出数组复制回原数组
        for (int i = 0; i < n; i++) {
            arr[i] = output[i];
        }
    }

    // 9. 桶排序 (Bucket Sort)
    // 适用于浮点数或均匀分布的数据
    // 简化版，假设数据在 [0, 1) 之间均匀分布
    public void bucketSort(double[] arr) {
        int n = arr.length;
        if (n <= 1) return;

        // 创建 n 个桶
        List<Double>[] buckets = new ArrayList[n];
        for (int i = 0; i < n; i++) {
            buckets[i] = new ArrayList<Double>();
        }

        // 将元素放入桶中
        for (int i = 0; i < n; i++) {
            int bucketIndex = (int) (arr[i] * n);
            buckets[bucketIndex].add(arr[i]);
        }

        // 对每个桶进行排序
        for (int i = 0; i < n; i++) {
            // 可以使用Collections.sort或者其他排序算法
            java.util.Collections.sort(buckets[i]);
        }

        // 将桶中的元素合并回原数组
        int index = 0;
        for (int i = 0; i < n; i++) {
            for (double num : buckets[i]) {
                arr[index++] = num;
            }
        }
    }

    // 10. 希尔排序 (Shell Sort)
    public void shellSort(int[] arr) {
        int n = arr.length;

        // 选择一个增量序列，通常使用 Knuth 序列 (h = h * 3 + 1)
        for (int gap = n / 2; gap > 0; gap /= 2) {
            // 对每个增量进行插入排序
            for (int i = gap; i < n; i++) {
                int temp = arr[i];
                int j;
                for (j = i; j >= gap && arr[j - gap] > temp; j -= gap) {
                    arr[j] = arr[j - gap];
                }
                arr[j] = temp;
            }
        }
    }

    public static void main(String[] args) {
        SortingAlgorithms sorter = new SortingAlgorithms();

        // 测试冒泡排序
        int[] arr1 = {64, 34, 25, 12, 22, 11, 90};
        System.out.println("原始数组 (冒泡): " + Arrays.toString(arr1));
        sorter.bubbleSort(arr1);
        System.out.println("冒泡排序结果: " + Arrays.toString(arr1));

        // 测试选择排序
        int[] arr2 = {64, 25, 12, 22, 11};
        System.out.println("原始数组 (选择): " + Arrays.toString(arr2));
        sorter.selectionSort(arr2);
        System.out.println("选择排序结果: " + Arrays.toString(arr2));

        // 测试插入排序
        int[] arr3 = {12, 11, 13, 5, 6};
        System.out.println("原始数组 (插入): " + Arrays.toString(arr3));
        sorter.insertionSort(arr3);
        System.out.println("插入排序结果: " + Arrays.toString(arr3));

        // 测试归并排序
        int[] arr4 = {38, 27, 43, 3, 9, 82, 10};
        System.out.println("原始数组 (归并): " + Arrays.toString(arr4));
        sorter.mergeSort(arr4);
        System.out.println("归并排序结果: " + Arrays.toString(arr4));

        // 测试快速排序
        int[] arr5 = {10, 80, 30, 90, 40, 50, 70};
        System.out.println("原始数组 (快速): " + Arrays.toString(arr5));
        sorter.quickSort(arr5);
        System.out.println("快速排序结果: " + Arrays.toString(arr5));

        // 测试堆排序
        int[] arr6 = {12, 11, 13, 5, 6, 7};
        System.out.println("原始数组 (堆): " + Arrays.toString(arr6));
        sorter.heapSort(arr6);
        System.out.println("堆排序结果: " + Arrays.toString(arr6));

        // 测试计数排序
        int[] arr7 = {4, 2, 2, 8, 3, 3, 1};
        System.out.println("原始数组 (计数): " + Arrays.toString(arr7));
        sorter.countingSort(arr7);
        System.out.println("计数排序结果: " + Arrays.toString(arr7));

        // 测试基数排序
        int[] arr8 = {170, 45, 75, 90, 802, 24, 2, 66};
        System.out.println("原始数组 (基数): " + Arrays.toString(arr8));
        sorter.radixSort(arr8);
        System.out.println("基数排序结果: " + Arrays.toString(arr8));

        // 测试桶排序
        // 注意：桶排序的输入通常是浮点数，且均匀分布
        double[] arr9 = {0.897, 0.565, 0.656, 0.1234, 0.665, 0.3434};
        System.out.println("原始数组 (桶): " + Arrays.toString(arr9));
        sorter.bucketSort(arr9);
        System.out.println("桶排序结果: " + Arrays.toString(arr9));

        // 测试希尔排序
        int[] arr10 = {12, 34, 54, 2, 3};
        System.out.println("原始数组 (希尔): " + Arrays.toString(arr10));
        sorter.shellSort(arr10);
        System.out.println("希尔排序结果: " + Arrays.toString(arr10));
    }
} 