package binarytree;

import java.util.*;

/**
 * 二叉树层序遍历算法实现
 * 包含基础层序遍历和按层分组的层序遍历
 */
public class LevelOrderTraversal {
    
    /**
     * 基础层序遍历 - 返回一维列表
     * 时间复杂度: O(n), 空间复杂度: O(w) 其中w是树的最大宽度
     */
    public List<Integer> levelOrder(TreeNode root) {
        List<Integer> result = new ArrayList<>();
        if (root == null) return result;
        
        // 使用队列进行广度优先搜索
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        
        while (!queue.isEmpty()) {
            TreeNode node = queue.poll();
            result.add(node.val);
            
            // 将左右子节点加入队列
            if (node.left != null) {
                queue.offer(node.left);
            }
            if (node.right != null) {
                queue.offer(node.right);
            }
        }
        
        return result;
    }
    
    /**
     * 按层分组的层序遍历 - 返回二维列表
     * 每一层作为一个子列表
     * 时间复杂度: O(n), 空间复杂度: O(w)
     */
    public List<List<Integer>> levelOrderByLevel(TreeNode root) {
        List<List<Integer>> result = new ArrayList<>();
        if (root == null) return result;
        
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        
        while (!queue.isEmpty()) {
            int levelSize = queue.size();  // 当前层的节点数量
            List<Integer> currentLevel = new ArrayList<>();
            
            // 处理当前层的所有节点
            for (int i = 0; i < levelSize; i++) {
                TreeNode node = queue.poll();
                currentLevel.add(node.val);
                
                // 将下一层的节点加入队列
                if (node.left != null) {
                    queue.offer(node.left);
                }
                if (node.right != null) {
                    queue.offer(node.right);
                }
            }
            
            result.add(currentLevel);
        }
        
        return result;
    }
    
    /**
     * 锯齿形层序遍历（之字形遍历）
     * 奇数层从左到右，偶数层从右到左
     * 时间复杂度: O(n), 空间复杂度: O(w)
     */
    public List<List<Integer>> zigzagLevelOrder(TreeNode root) {
        List<List<Integer>> result = new ArrayList<>();
        if (root == null) return result;
        
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        boolean leftToRight = true;  // 控制遍历方向
        
        while (!queue.isEmpty()) {
            int levelSize = queue.size();
            List<Integer> currentLevel = new ArrayList<>();
            
            for (int i = 0; i < levelSize; i++) {
                TreeNode node = queue.poll();
                
                // 根据方向决定添加位置
                if (leftToRight) {
                    currentLevel.add(node.val);
                } else {
                    currentLevel.add(0, node.val);  // 在开头添加
                }
                
                if (node.left != null) {
                    queue.offer(node.left);
                }
                if (node.right != null) {
                    queue.offer(node.right);
                }
            }
            
            result.add(currentLevel);
            leftToRight = !leftToRight;  // 改变方向
        }
        
        return result;
    }
    
    /**
     * 自底向上的层序遍历
     * 从叶子节点开始，逐层向上
     * 时间复杂度: O(n), 空间复杂度: O(w)
     */
    public List<List<Integer>> levelOrderBottom(TreeNode root) {
        List<List<Integer>> result = new ArrayList<>();
        if (root == null) return result;
        
        Queue<TreeNode> queue = new LinkedList<>();
        queue.offer(root);
        
        while (!queue.isEmpty()) {
            int levelSize = queue.size();
            List<Integer> currentLevel = new ArrayList<>();
            
            for (int i = 0; i < levelSize; i++) {
                TreeNode node = queue.poll();
                currentLevel.add(node.val);
                
                if (node.left != null) {
                    queue.offer(node.left);
                }
                if (node.right != null) {
                    queue.offer(node.right);
                }
            }
            
            // 在开头插入，实现自底向上
            result.add(0, currentLevel);
        }
        
        return result;
    }
    
    /**
     * 创建示例二叉树用于测试
     */
    public TreeNode createSampleTree() {
        // 创建二叉树:     1
        //              /   \
        //             2     3
        //            / \   / \
        //           4   5 6   7
        TreeNode root = new TreeNode(1);
        root.left = new TreeNode(2);
        root.right = new TreeNode(3);
        root.left.left = new TreeNode(4);
        root.left.right = new TreeNode(5);
        root.right.left = new TreeNode(6);
        root.right.right = new TreeNode(7);
        
        return root;
    }
    
    /**
     * 创建更复杂的示例二叉树
     */
    public TreeNode createComplexTree() {
        // 创建二叉树:       1
        //                /   \
        //               2     3
        //              / \   / \
        //             4   5 6   7
        //            / \   \
        //           8   9   10
        TreeNode root = new TreeNode(1);
        root.left = new TreeNode(2);
        root.right = new TreeNode(3);
        root.left.left = new TreeNode(4);
        root.left.right = new TreeNode(5);
        root.right.left = new TreeNode(6);
        root.right.right = new TreeNode(7);
        root.left.left.left = new TreeNode(8);
        root.left.left.right = new TreeNode(9);
        root.left.right.right = new TreeNode(10);
        
        return root;
    }
    
    /**
     * 测试所有层序遍历算法
     */
    public static void main(String[] args) {
        LevelOrderTraversal traversal = new LevelOrderTraversal();
        
        System.out.println("=== 层序遍历算法测试 ===\n");
        
        // 测试基础二叉树
        System.out.println("基础二叉树测试:");
        TreeNode root1 = traversal.createSampleTree();
        System.out.println("基础层序遍历: " + traversal.levelOrder(root1));
        System.out.println("按层分组: " + traversal.levelOrderByLevel(root1));
        System.out.println("锯齿形层序: " + traversal.zigzagLevelOrder(root1));
        System.out.println("自底向上层序: " + traversal.levelOrderBottom(root1));
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 测试复杂二叉树
        System.out.println("复杂二叉树测试:");
        TreeNode root2 = traversal.createComplexTree();
        System.out.println("基础层序遍历: " + traversal.levelOrder(root2));
        System.out.println("按层分组: " + traversal.levelOrderByLevel(root2));
        System.out.println("锯齿形层序: " + traversal.zigzagLevelOrder(root2));
        System.out.println("自底向上层序: " + traversal.levelOrderBottom(root2));
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 测试空树
        System.out.println("空树测试:");
        System.out.println("基础层序遍历: " + traversal.levelOrder(null));
        System.out.println("按层分组: " + traversal.levelOrderByLevel(null));
        System.out.println("锯齿形层序: " + traversal.zigzagLevelOrder(null));
        System.out.println("自底向上层序: " + traversal.levelOrderBottom(null));
        
        System.out.println("\n=== 算法说明 ===");
        System.out.println("1. 基础层序遍历: 按层级顺序输出所有节点值");
        System.out.println("2. 按层分组: 每一层作为一个子列表返回");
        System.out.println("3. 锯齿形层序: 奇数层从左到右，偶数层从右到左");
        System.out.println("4. 自底向上: 从叶子节点开始，逐层向上");
    }
} 