package linkedlist;

/**
 * 链表环检测算法
 * 包含检测环和找到环入口的算法
 */
public class LinkedListCycle {
    
    /**
     * 检测链表是否有环
     * 使用快慢指针法
     * 时间复杂度: O(n)
     * 空间复杂度: O(1)
     * @param head 链表头节点
     * @return 是否有环
     */
    public boolean hasCycle(ListNode head) {
        if (head == null || head.next == null) return false;
        
        ListNode slow = head;
        ListNode fast = head;
        
        while (fast != null && fast.next != null) {
            slow = slow.next;
            fast = fast.next.next;
            
            if (slow == fast) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 找到环形链表的入口节点
     * 使用快慢指针法
     * 时间复杂度: O(n)
     * 空间复杂度: O(1)
     * @param head 链表头节点
     * @return 环的入口节点，如果没有环返回null
     */
    public ListNode detectCycle(ListNode head) {
        if (head == null || head.next == null) return null;
        
        ListNode slow = head;
        ListNode fast = head;
        
        // 找到相遇点
        while (fast != null && fast.next != null) {
            slow = slow.next;
            fast = fast.next.next;
            
            if (slow == fast) {
                break;
            }
        }
        
        if (fast == null || fast.next == null) {
            return null; // 没有环
        }
        
        // 找到环的入口
        slow = head;
        while (slow != fast) {
            slow = slow.next;
            fast = fast.next;
        }
        
        return slow;
    }
    
    /**
     * 计算环的长度
     * @param head 链表头节点
     * @return 环的长度，如果没有环返回0
     */
    public int cycleLength(ListNode head) {
        if (head == null || head.next == null) return 0;
        
        ListNode slow = head;
        ListNode fast = head;
        
        // 找到相遇点
        while (fast != null && fast.next != null) {
            slow = slow.next;
            fast = fast.next.next;
            
            if (slow == fast) {
                break;
            }
        }
        
        if (fast == null || fast.next == null) {
            return 0; // 没有环
        }
        
        // 计算环的长度
        int length = 1;
        slow = slow.next;
        while (slow != fast) {
            slow = slow.next;
            length++;
        }
        
        return length;
    }
    
    /**
     * 创建带环的链表（用于测试）
     * @param arr 数组
     * @param pos 环的入口位置（从0开始）
     * @return 带环的链表
     */
    public ListNode createCycleList(int[] arr, int pos) {
        if (arr == null || arr.length == 0) return null;
        
        ListNode head = new ListNode(arr[0]);
        ListNode current = head;
        ListNode cycleStart = null;
        
        for (int i = 1; i < arr.length; i++) {
            current.next = new ListNode(arr[i]);
            current = current.next;
            
            if (i == pos) {
                cycleStart = current;
            }
        }
        
        // 创建环
        if (pos >= 0 && pos < arr.length) {
            current.next = cycleStart;
        }
        
        return head;
    }
    
    /**
     * 测试环检测算法
     */
    public static void main(String[] args) {
        LinkedListCycle cycle = new LinkedListCycle();
        LinkedListBasicOperations operations = new LinkedListBasicOperations();
        
        // 测试无环链表
        System.out.println("=== 环检测测试 ===");
        int[] arr1 = {1, 2, 3, 4, 5};
        ListNode list1 = operations.createLinkedList(arr1);
        System.out.print("无环链表: ");
        operations.printLinkedList(list1);
        System.out.println("是否有环: " + cycle.hasCycle(list1));
        
        // 测试有环链表
        ListNode list2 = cycle.createCycleList(arr1, 2);
        System.out.print("有环链表（环入口在位置2）: ");
        // 注意：不能直接打印有环链表，会导致无限循环
        System.out.println("1 -> 2 -> 3 -> 4 -> 5 -> 3 (环)");
        System.out.println("是否有环: " + cycle.hasCycle(list2));
        
        // 测试环入口检测
        ListNode cycleEntry = cycle.detectCycle(list2);
        if (cycleEntry != null) {
            System.out.println("环的入口节点值: " + cycleEntry.val);
        } else {
            System.out.println("没有找到环的入口");
        }
        
        // 测试环长度
        int length = cycle.cycleLength(list2);
        System.out.println("环的长度: " + length);
        
        // 测试自环
        ListNode selfCycle = new ListNode(1);
        selfCycle.next = selfCycle;
        System.out.println("自环链表是否有环: " + cycle.hasCycle(selfCycle));
        
        // 测试两个节点的环
        ListNode twoNodeCycle = new ListNode(1);
        twoNodeCycle.next = new ListNode(2);
        twoNodeCycle.next.next = twoNodeCycle;
        System.out.println("两节点环是否有环: " + cycle.hasCycle(twoNodeCycle));
    }
} 