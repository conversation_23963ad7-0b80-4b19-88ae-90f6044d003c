{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAC3B,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAC3B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAG7B,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AACrD,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC1B,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,MAAM,CAAC;AAKhD,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,QAAQ,IAAI,MAAM,CAAC,EAAE,GAAG,QAAQ,GAAG,KAAK,CAAC;AAE/E,KAAK,cAAc,GAAG;IACrB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IACnD,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;CACpD,CAAC;AAEF,KAAK,WAAW,CAAC,CAAC,IAAI;KACpB,CAAC,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAC/C,cAAc,CAAC,CAAC,CAAC,GACjB,KAAK;CACR,CAAC,MAAM,cAAc,CAAC,CAAC;AAExB,MAAM,MAAM,qBAAqB,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,GACpD,IAAI,CAAC,YAAY,GAAG;IACnB,OAAO,CAAC,EAAE,mBAAmB,GAAG,CAAC,MAAM,mBAAmB,CAAC,CAAC;CAC5D,CAAC;AAEH,UAAU,2BAA4B,SAAQ,IAAI,CAAC,aAAa;IAC/D,UAAU,CAAC,EAAE;QACZ,IAAI,EAAE,MAAM,CAAC;KACb,EAAE,CAAC;IACJ,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,eAAe,IAAI,IAAI,CAAC;CACxB;AAED;;;GAGG;AACH,qBAAa,cAAc,CAAC,GAAG,SAAS,MAAM,CAAE,SAAQ,KAAK;IAC5D,MAAM,CAAC,SAAS,6BAA8B;IAE9C,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;IACpB,YAAY,EAAE,mBAAmB,GAAG,CAAC,MAAM,mBAAmB,CAAC,CAAC;IAChE,WAAW,EAAE,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,iBAAiB,CAAC;gBAE/C,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,qBAAqB,CAAC,GAAG,CAAC;IAuB/D,UAAU,CAAC,GAAG,EAAE,2BAA2B,EAAE,IAAI,EAAE,gBAAgB,GAAG,IAAI;IAO1E,eAAe,CACd,GAAG,EAAE,2BAA2B,EAChC,IAAI,EAAE,gBAAgB,GACpB,IAAI;IA0CD,OAAO,CACZ,GAAG,EAAE,2BAA2B,EAChC,IAAI,EAAE,gBAAgB,GACpB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;CA2CtB"}