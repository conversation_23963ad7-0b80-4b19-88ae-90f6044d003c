import { NextRequest, NextResponse } from 'next/server'
import { BlogPost } from '@/lib/types'

// 模拟博客文章数据 (与 blog/route.ts 中的数据保持一致)
const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: 'Next.js App Router 入门指南',
    excerpt: '学习如何使用 Next.js 13+ 的新 App Router 功能来构建现代化的 React 应用程序。',
    content: `
      <h2>什么是 App Router？</h2>
      <p>Next.js 13 引入了新的 App Router，这是一个基于 React Server Components 的全新路由系统。它提供了更好的性能、更简洁的 API 和更强大的功能。</p>

      <h2>主要特性</h2>
      <ul>
        <li><strong>服务端组件</strong>：默认情况下，所有组件都在服务端渲染</li>
        <li><strong>嵌套布局</strong>：支持嵌套和共享布局</li>
        <li><strong>加载状态</strong>：内置的加载和错误状态处理</li>
        <li><strong>流式渲染</strong>：支持流式渲染，提高页面加载速度</li>
      </ul>

      <h2>文件结构</h2>
      <p>App Router 使用文件夹和文件来定义路由：</p>
      <ul>
        <li><code>page.tsx</code> - 页面组件</li>
        <li><code>layout.tsx</code> - 布局组件</li>
        <li><code>loading.tsx</code> - 加载状态组件</li>
        <li><code>error.tsx</code> - 错误状态组件</li>
      </ul>

      <h2>开始使用</h2>
      <p>创建一个新的 Next.js 项目并启用 App Router：</p>
      <pre><code>npx create-next-app@latest --app</code></pre>

      <p>这将创建一个带有 App Router 的新项目，你可以立即开始构建现代化的 React 应用程序。</p>
    `,
    date: '2024-01-15',
    author: '张三',
    category: 'Next.js',
    readTime: '5 分钟',
    tags: ['Next.js', 'React', 'App Router'],
    published: true,
  },
  // ... 其他文章数据
]

interface RouteParams {
  params: { id: string }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const id = parseInt(params.id)
  const post = blogPosts.find(p => p.id === id && p.published)

  if (!post) {
    return NextResponse.json(
      { success: false, error: '博客文章未找到' },
      { status: 404 }
    )
  }

  return NextResponse.json({
    success: true,
    data: post,
  })
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const id = parseInt(params.id)
    const body: Partial<BlogPost> = await request.json()
    
    const postIndex = blogPosts.findIndex(p => p.id === id)
    
    if (postIndex === -1) {
      return NextResponse.json(
        { success: false, error: '博客文章未找到' },
        { status: 404 }
      )
    }

    // 这里可以添加身份验证逻辑
    // 验证用户是否有权限编辑此博客文章

    const updatedPost = {
      ...blogPosts[postIndex],
      ...body,
      id, // 确保 ID 不被修改
    }

    blogPosts[postIndex] = updatedPost as BlogPost

    return NextResponse.json({
      success: true,
      data: updatedPost,
      message: '博客文章更新成功',
    })

  } catch (error) {
    console.error('更新博客文章错误:', error)
    return NextResponse.json(
      { success: false, error: '更新博客文章失败' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const id = parseInt(params.id)
    const postIndex = blogPosts.findIndex(p => p.id === id)
    
    if (postIndex === -1) {
      return NextResponse.json(
        { success: false, error: '博客文章未找到' },
        { status: 404 }
      )
    }

    // 这里可以添加身份验证逻辑
    // 验证用户是否有权限删除此博客文章

    const deletedPost = blogPosts.splice(postIndex, 1)[0]

    return NextResponse.json({
      success: true,
      data: deletedPost,
      message: '博客文章删除成功',
    })

  } catch (error) {
    console.error('删除博客文章错误:', error)
    return NextResponse.json(
      { success: false, error: '删除博客文章失败' },
      { status: 500 }
    )
  }
}