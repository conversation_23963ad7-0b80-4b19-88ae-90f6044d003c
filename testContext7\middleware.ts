import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// 中间件函数
export function middleware(request: NextRequest) {
  // 添加你的中间件逻辑
  console.log('Middleware 执行:', request.nextUrl.pathname)
  
  // 示例：添加自定义响应头
  const response = NextResponse.next()
  response.headers.set('x-middleware-executed', 'true')
  
  return response
}

// 配置中间件匹配器
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了：
     * - api (API 路由)
     * - _next/static (静态文件)
     * - _next/image (图片优化文件)
     * - favicon.ico (网站图标)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}