import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '博客 - Next.js App Router',
  description: '技术文章、教程和最新见解',
}

// 模拟博客文章数据
const blogPosts = [
  {
    id: 1,
    title: 'Next.js App Router 入门指南',
    excerpt: '学习如何使用 Next.js 13+ 的新 App Router 功能来构建现代化的 React 应用程序。',
    date: '2024-01-15',
    author: '张三',
    category: 'Next.js',
    readTime: '5 分钟'
  },
  {
    id: 2,
    title: 'TypeScript 在 React 项目中的最佳实践',
    excerpt: '探索在 React 和 Next.js 项目中使用 TypeScript 的技巧和最佳实践。',
    date: '2024-01-10',
    author: '李四',
    category: 'TypeScript',
    readTime: '8 分钟'
  },
  {
    id: 3,
    title: 'Tailwind CSS 设计系统构建',
    excerpt: '如何使用 Tailwind CSS 创建一致性的设计系统，提高开发效率。',
    date: '2024-01-05',
    author: '王五',
    category: 'CSS',
    readTime: '6 分钟'
  }
]

export default function BlogPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">技术博客</h1>
        <p className="text-xl text-gray-600">
          分享最新的技术见解、教程和开发经验
        </p>
      </div>

      <div className="space-y-8">
        {blogPosts.map((post) => (
          <article 
            key={post.id}
            className="bg-white rounded-lg shadow-md border hover:shadow-lg transition-shadow p-6"
          >
            <div className="flex items-center justify-between mb-3">
              <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                {post.category}
              </span>
              <div className="text-sm text-gray-500">
                {post.readTime}
              </div>
            </div>
            
            <h2 className="text-2xl font-semibold text-gray-900 mb-3">
              <Link 
                href={`/blog/${post.id}`}
                className="hover:text-blue-600 transition-colors"
              >
                {post.title}
              </Link>
            </h2>
            
            <p className="text-gray-600 mb-4 leading-relaxed">
              {post.excerpt}
            </p>
            
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center space-x-4">
                <span>作者: {post.author}</span>
                <span>发布于: {post.date}</span>
              </div>
              <Link 
                href={`/blog/${post.id}`}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                阅读全文 →
              </Link>
            </div>
          </article>
        ))}
      </div>

      <div className="mt-12 text-center">
        <div className="bg-gray-50 rounded-lg p-8">
          <h3 className="text-xl font-semibold mb-3">订阅我们的博客</h3>
          <p className="text-gray-600 mb-4">
            获取最新的技术文章和教程，直接发送到您的邮箱
          </p>
          <div className="flex justify-center">
            <div className="flex max-w-md">
              <input
                type="email"
                placeholder="输入您的邮箱地址"
                className="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-r-md">
                订阅
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}