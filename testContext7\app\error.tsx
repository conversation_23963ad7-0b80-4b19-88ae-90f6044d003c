'use client'

import { useEffect } from 'react'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 在这里可以记录错误到错误报告服务
    console.error(error)
  }, [error])

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-red-600 mb-2">出现了一些问题！</h2>
        <p className="text-gray-600">
          抱歉，页面加载时遇到了错误。请尝试刷新页面或稍后再试。
        </p>
      </div>
      
      <button
        onClick={reset}
        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        重试
      </button>
      
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-6 p-4 bg-gray-100 rounded">
          <summary className="cursor-pointer font-semibold">
            错误详情 (开发环境)
          </summary>
          <pre className="mt-2 text-sm text-left overflow-auto">
            {error.message}
          </pre>
        </details>
      )}
    </div>
  )
}