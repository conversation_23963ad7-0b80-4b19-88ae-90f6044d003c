import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '关于我们 - Next.js App Router',
  description: '了解我们的团队、使命和愿景',
}

export default function AboutPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold text-gray-900 mb-6">关于我们</h1>
      
      <div className="prose prose-lg max-w-none">
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">我们的使命</h2>
          <p className="text-gray-700 leading-relaxed">
            我们致力于创建现代化、高性能的 Web 应用程序，使用最新的技术栈和最佳实践。
            通过 Next.js App Router，我们能够构建快速、可扩展且用户友好的数字体验。
          </p>
        </section>

        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">技术栈</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">前端技术</h3>
              <ul className="text-blue-800 space-y-1">
                <li>• Next.js 15 (App Router)</li>
                <li>• React 18</li>
                <li>• TypeScript</li>
                <li>• Tailwind CSS</li>
              </ul>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">开发工具</h3>
              <ul className="text-green-800 space-y-1">
                <li>• ESLint</li>
                <li>• Prettier</li>
                <li>• Git</li>
                <li>• VS Code</li>
              </ul>
            </div>
          </div>
        </section>

        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">我们的价值观</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🚀</span>
              </div>
              <h3 className="font-semibold mb-2">创新</h3>
              <p className="text-gray-600 text-sm">
                持续探索新技术，推动行业发展
              </p>
            </div>
            <div className="text-center">
              <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="font-semibold mb-2">性能</h3>
              <p className="text-gray-600 text-sm">
                追求极致的用户体验和应用性能
              </p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="font-semibold mb-2">协作</h3>
              <p className="text-gray-600 text-sm">
                团队合作，共同创造优秀产品
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}