import { NextRequest, NextResponse } from 'next/server'
import { BlogPost } from '@/lib/types'

// 模拟博客文章数据
const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: 'Next.js App Router 入门指南',
    excerpt: '学习如何使用 Next.js 13+ 的新 App Router 功能来构建现代化的 React 应用程序。',
    content: '详细的博客内容...',
    date: '2024-01-15',
    author: '张三',
    category: 'Next.js',
    readTime: '5 分钟',
    tags: ['Next.js', 'React', 'App Router'],
    published: true,
  },
  {
    id: 2,
    title: 'TypeScript 在 React 项目中的最佳实践',
    excerpt: '探索在 React 和 Next.js 项目中使用 TypeScript 的技巧和最佳实践。',
    content: '详细的博客内容...',
    date: '2024-01-10',
    author: '李四',
    category: 'TypeScript',
    readTime: '8 分钟',
    tags: ['TypeScript', 'React', '最佳实践'],
    published: true,
  },
  {
    id: 3,
    title: 'Tailwind CSS 设计系统构建',
    excerpt: '如何使用 Tailwind CSS 创建一致性的设计系统，提高开发效率。',
    content: '详细的博客内容...',
    date: '2024-01-05',
    author: '王五',
    category: 'CSS',
    readTime: '6 分钟',
    tags: ['Tailwind CSS', '设计系统', 'CSS'],
    published: true,
  },
]

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  const category = searchParams.get('category')
  const search = searchParams.get('search')

  let filteredPosts = blogPosts.filter(post => post.published)

  // 按分类筛选
  if (category) {
    filteredPosts = filteredPosts.filter(post => 
      post.category.toLowerCase() === category.toLowerCase()
    )
  }

  // 搜索功能
  if (search) {
    const searchTerm = search.toLowerCase()
    filteredPosts = filteredPosts.filter(post =>
      post.title.toLowerCase().includes(searchTerm) ||
      post.excerpt.toLowerCase().includes(searchTerm) ||
      post.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  // 分页
  const total = filteredPosts.length
  const totalPages = Math.ceil(total / limit)
  const offset = (page - 1) * limit
  const paginatedPosts = filteredPosts.slice(offset, offset + limit)

  return NextResponse.json({
    success: true,
    data: paginatedPosts,
    meta: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  })
}

export async function POST(request: NextRequest) {
  try {
    const body: Partial<BlogPost> = await request.json()
    
    // 这里可以添加身份验证逻辑
    // 验证用户是否有权限创建博客文章
    
    const newPost: BlogPost = {
      id: Math.max(...blogPosts.map(p => p.id)) + 1,
      title: body.title || '',
      excerpt: body.excerpt || '',
      content: body.content || '',
      date: new Date().toISOString().split('T')[0],
      author: body.author || '匿名',
      category: body.category || '未分类',
      readTime: body.readTime || '5 分钟',
      tags: body.tags || [],
      published: body.published ?? false,
    }

    blogPosts.push(newPost)

    return NextResponse.json({
      success: true,
      data: newPost,
      message: '博客文章创建成功',
    })

  } catch (error) {
    console.error('创建博客文章错误:', error)
    return NextResponse.json(
      { success: false, error: '创建博客文章失败' },
      { status: 500 }
    )
  }
}