package linkedlist;

/**
 * 链表反转算法
 * 包含迭代和递归两种实现方式
 */
public class LinkedListReverse {
    
    /**
     * 迭代法反转链表
     * 时间复杂度: O(n)
     * 空间复杂度: O(1)
     * @param head 链表头节点
     * @return 反转后的链表头节点
     */
    public ListNode reverseList(ListNode head) {
        ListNode prev = null;
        ListNode current = head;
        
        while (current != null) {
            ListNode nextTemp = current.next;
            current.next = prev;
            prev = current;
            current = nextTemp;
        }
        
        return prev;
    }
    
    /**
     * 递归法反转链表
     * 时间复杂度: O(n)
     * 空间复杂度: O(n) - 递归调用栈
     * @param head 链表头节点
     * @return 反转后的链表头节点
     */
    public ListNode reverseListRecursive(ListNode head) {
        if (head == null || head.next == null) {
            return head;
        }
        
        ListNode newHead = reverseListRecursive(head.next);
        head.next.next = head;
        head.next = null;
        
        return newHead;
    }
    
    /**
     * 反转链表的一部分（从位置m到n）
     * @param head 链表头节点
     * @param m 开始位置（从1开始）
     * @param n 结束位置
     * @return 反转后的链表头节点
     */
    public ListNode reverseBetween(ListNode head, int m, int n) {
        if (head == null || m == n) return head;
        
        ListNode dummy = new ListNode(0);
        dummy.next = head;
        ListNode prev = dummy;
        
        // 移动到第m个节点的前一个节点
        for (int i = 0; i < m - 1; i++) {
            prev = prev.next;
        }
        
        ListNode start = prev.next;
        ListNode then = start.next;
        
        // 反转从m到n的部分
        for (int i = 0; i < n - m; i++) {
            start.next = then.next;
            then.next = prev.next;
            prev.next = then;
            then = start.next;
        }
        
        return dummy.next;
    }
    
    /**
     * K个一组反转链表
     * @param head 链表头节点
     * @param k 每组节点数
     * @return 反转后的链表头节点
     */
    public ListNode reverseKGroup(ListNode head, int k) {
        if (head == null || k == 1) return head;
        
        ListNode dummy = new ListNode(0);
        dummy.next = head;
        ListNode prev = dummy;
        
        int count = 0;
        ListNode current = head;
        
        while (current != null) {
            count++;
            if (count % k == 0) {
                prev = reverseBetween(prev, current.next);
                current = prev.next;
            } else {
                current = current.next;
            }
        }
        
        return dummy.next;
    }
    
    /**
     * 辅助方法：反转指定范围内的链表
     */
    private ListNode reverseBetween(ListNode start, ListNode end) {
        ListNode prev = start;
        ListNode current = start.next;
        ListNode first = current;
        
        while (current != end) {
            ListNode next = current.next;
            current.next = prev;
            prev = current;
            current = next;
        }
        
        start.next = prev;
        first.next = current;
        
        return first;
    }
    
    /**
     * 测试反转算法
     */
    public static void main(String[] args) {
        LinkedListReverse reverse = new LinkedListReverse();
        LinkedListBasicOperations operations = new LinkedListBasicOperations();
        
        // 测试迭代法反转
        System.out.println("=== 链表反转测试 ===");
        int[] arr = {1, 2, 3, 4, 5};
        ListNode list = operations.createLinkedList(arr);
        System.out.print("原始链表: ");
        operations.printLinkedList(list);
        
        ListNode reversed = reverse.reverseList(list);
        System.out.print("迭代法反转后: ");
        operations.printLinkedList(reversed);
        
        // 测试递归法反转
        ListNode list2 = operations.createLinkedList(arr);
        System.out.print("原始链表: ");
        operations.printLinkedList(list2);
        
        ListNode reversedRecursive = reverse.reverseListRecursive(list2);
        System.out.print("递归法反转后: ");
        operations.printLinkedList(reversedRecursive);
        
        // 测试部分反转
        ListNode list3 = operations.createLinkedList(arr);
        System.out.print("原始链表: ");
        operations.printLinkedList(list3);
        
        ListNode partialReversed = reverse.reverseBetween(list3, 2, 4);
        System.out.print("反转位置2-4后: ");
        operations.printLinkedList(partialReversed);
        
        // 测试K个一组反转
        int[] arr2 = {1, 2, 3, 4, 5, 6, 7, 8};
        ListNode list4 = operations.createLinkedList(arr2);
        System.out.print("原始链表: ");
        operations.printLinkedList(list4);
        
        ListNode kReversed = reverse.reverseKGroup(list4, 3);
        System.out.print("K=3分组反转后: ");
        operations.printLinkedList(kReversed);
    }
} 