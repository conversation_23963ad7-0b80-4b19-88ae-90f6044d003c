package stack_queue;

import java.util.*;

/**
 * 栈和队列算法
 * 包含栈和队列的基本操作以及经典算法
 */
public class StackQueueAlgorithms {
    
    /**
     * 用栈实现队列
     */
    static class MyQueue {
        private Stack<Integer> stack1; // 用于入队
        private Stack<Integer> stack2; // 用于出队
        
        public MyQueue() {
            stack1 = new Stack<>();
            stack2 = new Stack<>();
        }
        
        public void push(int x) {
            stack1.push(x);
        }
        
        public int pop() {
            if (stack2.isEmpty()) {
                while (!stack1.isEmpty()) {
                    stack2.push(stack1.pop());
                }
            }
            return stack2.pop();
        }
        
        public int peek() {
            if (stack2.isEmpty()) {
                while (!stack1.isEmpty()) {
                    stack2.push(stack1.pop());
                }
            }
            return stack2.peek();
        }
        
        public boolean empty() {
            return stack1.isEmpty() && stack2.isEmpty();
        }
    }
    
    /**
     * 用队列实现栈
     */
    static class MyStack {
        private Queue<Integer> queue1;
        private Queue<Integer> queue2;
        
        public MyStack() {
            queue1 = new LinkedList<>();
            queue2 = new LinkedList<>();
        }
        
        public void push(int x) {
            queue2.offer(x);
            while (!queue1.isEmpty()) {
                queue2.offer(queue1.poll());
            }
            Queue<Integer> temp = queue1;
            queue1 = queue2;
            queue2 = temp;
        }
        
        public int pop() {
            return queue1.poll();
        }
        
        public int top() {
            return queue1.peek();
        }
        
        public boolean empty() {
            return queue1.isEmpty();
        }
    }
    
    /**
     * 有效的括号
     * 判断字符串中的括号是否有效
     */
    public boolean isValid(String s) {
        Stack<Character> stack = new Stack<>();
        
        for (char c : s.toCharArray()) {
            if (c == '(' || c == '{' || c == '[') {
                stack.push(c);
            } else {
                if (stack.isEmpty()) return false;
                
                char top = stack.pop();
                if ((c == ')' && top != '(') ||
                    (c == '}' && top != '{') ||
                    (c == ']' && top != '[')) {
                    return false;
                }
            }
        }
        
        return stack.isEmpty();
    }
    
    /**
     * 最小栈
     * 支持在常数时间内获取最小元素的栈
     */
    static class MinStack {
        private Stack<Integer> stack;
        private Stack<Integer> minStack;
        
        public MinStack() {
            stack = new Stack<>();
            minStack = new Stack<>();
        }
        
        public void push(int val) {
            stack.push(val);
            if (minStack.isEmpty() || val <= minStack.peek()) {
                minStack.push(val);
            }
        }
        
        public void pop() {
            if (stack.pop().equals(minStack.peek())) {
                minStack.pop();
            }
        }
        
        public int top() {
            return stack.peek();
        }
        
        public int getMin() {
            return minStack.peek();
        }
    }
    
    /**
     * 单调栈：下一个更大元素
     */
    public int[] nextGreaterElement(int[] nums) {
        int n = nums.length;
        int[] result = new int[n];
        Arrays.fill(result, -1);
        
        Stack<Integer> stack = new Stack<>();
        
        for (int i = 0; i < n; i++) {
            while (!stack.isEmpty() && nums[stack.peek()] < nums[i]) {
                result[stack.pop()] = nums[i];
            }
            stack.push(i);
        }
        
        return result;
    }
    
    /**
     * 单调队列：滑动窗口最大值
     */
    public int[] maxSlidingWindow(int[] nums, int k) {
        if (nums == null || nums.length == 0 || k <= 0) {
            return new int[0];
        }
        
        int n = nums.length;
        int[] result = new int[n - k + 1];
        Deque<Integer> deque = new LinkedList<>();
        
        for (int i = 0; i < n; i++) {
            // 移除队列中不在当前窗口的元素
            while (!deque.isEmpty() && deque.peekFirst() < i - k + 1) {
                deque.pollFirst();
            }
            
            // 移除队列中比当前元素小的元素
            while (!deque.isEmpty() && nums[deque.peekLast()] < nums[i]) {
                deque.pollLast();
            }
            
            deque.offerLast(i);
            
            // 记录当前窗口的最大值
            if (i >= k - 1) {
                result[i - k + 1] = nums[deque.peekFirst()];
            }
        }
        
        return result;
    }
    
    /**
     * 逆波兰表达式求值
     */
    public int evalRPN(String[] tokens) {
        Stack<Integer> stack = new Stack<>();
        
        for (String token : tokens) {
            if (token.equals("+")) {
                int b = stack.pop();
                int a = stack.pop();
                stack.push(a + b);
            } else if (token.equals("-")) {
                int b = stack.pop();
                int a = stack.pop();
                stack.push(a - b);
            } else if (token.equals("*")) {
                int b = stack.pop();
                int a = stack.pop();
                stack.push(a * b);
            } else if (token.equals("/")) {
                int b = stack.pop();
                int a = stack.pop();
                stack.push(a / b);
            } else {
                stack.push(Integer.parseInt(token));
            }
        }
        
        return stack.pop();
    }
    
    /**
     * 测试栈和队列算法
     */
    public static void main(String[] args) {
        StackQueueAlgorithms algo = new StackQueueAlgorithms();
        
        // 测试用栈实现队列
        System.out.println("=== 用栈实现队列测试 ===");
        MyQueue queue = new MyQueue();
        queue.push(1);
        queue.push(2);
        queue.push(3);
        System.out.println("队列头部: " + queue.peek());
        System.out.println("出队: " + queue.pop());
        System.out.println("队列是否为空: " + queue.empty());
        
        // 测试用队列实现栈
        System.out.println("\n=== 用队列实现栈测试 ===");
        MyStack stack = new MyStack();
        stack.push(1);
        stack.push(2);
        stack.push(3);
        System.out.println("栈顶: " + stack.top());
        System.out.println("出栈: " + stack.pop());
        System.out.println("栈是否为空: " + stack.empty());
        
        // 测试有效的括号
        System.out.println("\n=== 有效的括号测试 ===");
        String[] brackets = {"()", "()[]{}", "(]", "([)]", "{[]}"};
        for (String s : brackets) {
            System.out.println(s + " 是否有效: " + algo.isValid(s));
        }
        
        // 测试最小栈
        System.out.println("\n=== 最小栈测试 ===");
        MinStack minStack = new MinStack();
        minStack.push(-2);
        minStack.push(0);
        minStack.push(-3);
        System.out.println("最小值: " + minStack.getMin());
        minStack.pop();
        System.out.println("栈顶: " + minStack.top());
        System.out.println("最小值: " + minStack.getMin());
        
        // 测试下一个更大元素
        System.out.println("\n=== 下一个更大元素测试 ===");
        int[] nums = {4, 5, 2, 10};
        int[] nextGreater = algo.nextGreaterElement(nums);
        System.out.println("原数组: " + Arrays.toString(nums));
        System.out.println("下一个更大元素: " + Arrays.toString(nextGreater));
        
        // 测试滑动窗口最大值
        System.out.println("\n=== 滑动窗口最大值测试 ===");
        int[] windowNums = {1, 3, -1, -3, 5, 3, 6, 7};
        int k = 3;
        int[] maxWindow = algo.maxSlidingWindow(windowNums, k);
        System.out.println("原数组: " + Arrays.toString(windowNums));
        System.out.println("窗口大小: " + k);
        System.out.println("滑动窗口最大值: " + Arrays.toString(maxWindow));
        
        // 测试逆波兰表达式
        System.out.println("\n=== 逆波兰表达式测试 ===");
        String[] rpn = {"2", "1", "+", "3", "*"};
        int result = algo.evalRPN(rpn);
        System.out.println("逆波兰表达式: " + Arrays.toString(rpn));
        System.out.println("计算结果: " + result);
    }
} 