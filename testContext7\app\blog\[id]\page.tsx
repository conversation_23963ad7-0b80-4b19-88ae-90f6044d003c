import { Metadata } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'

// 模拟博客文章数据
const blogPosts = {
  1: {
    id: 1,
    title: 'Next.js App Router 入门指南',
    content: `
      <h2>什么是 App Router？</h2>
      <p>Next.js 13 引入了新的 App Router，这是一个基于 React Server Components 的全新路由系统。它提供了更好的性能、更简洁的 API 和更强大的功能。</p>

      <h2>主要特性</h2>
      <ul>
        <li><strong>服务端组件</strong>：默认情况下，所有组件都在服务端渲染</li>
        <li><strong>嵌套布局</strong>：支持嵌套和共享布局</li>
        <li><strong>加载状态</strong>：内置的加载和错误状态处理</li>
        <li><strong>流式渲染</strong>：支持流式渲染，提高页面加载速度</li>
      </ul>

      <h2>文件结构</h2>
      <p>App Router 使用文件夹和文件来定义路由：</p>
      <ul>
        <li><code>page.tsx</code> - 页面组件</li>
        <li><code>layout.tsx</code> - 布局组件</li>
        <li><code>loading.tsx</code> - 加载状态组件</li>
        <li><code>error.tsx</code> - 错误状态组件</li>
      </ul>

      <h2>开始使用</h2>
      <p>创建一个新的 Next.js 项目并启用 App Router：</p>
      <pre><code>npx create-next-app@latest --app</code></pre>

      <p>这将创建一个带有 App Router 的新项目，你可以立即开始构建现代化的 React 应用程序。</p>
    `,
    date: '2024-01-15',
    author: '张三',
    category: 'Next.js',
    readTime: '5 分钟'
  },
  2: {
    id: 2,
    title: 'TypeScript 在 React 项目中的最佳实践',
    content: `
      <h2>为什么选择 TypeScript？</h2>
      <p>TypeScript 为 JavaScript 添加了静态类型检查，可以在开发阶段捕获错误，提高代码质量和开发效率。</p>

      <h2>在 React 中的应用</h2>
      <p>TypeScript 与 React 的结合提供了强大的类型安全保障：</p>
      <ul>
        <li>组件 Props 类型定义</li>
        <li>State 和 Hook 的类型推导</li>
        <li>事件处理器的类型安全</li>
      </ul>

      <h2>最佳实践</h2>
      <ol>
        <li><strong>严格模式</strong>：启用 TypeScript 严格模式</li>
        <li><strong>接口定义</strong>：为 Props 和 State 定义清晰的接口</li>
        <li><strong>泛型使用</strong>：合理使用泛型提高代码复用性</li>
        <li><strong>类型推导</strong>：让 TypeScript 尽可能进行类型推导</li>
      </ol>

      <p>通过这些实践，你可以构建更加健壮和可维护的 React 应用程序。</p>
    `,
    date: '2024-01-10',
    author: '李四',
    category: 'TypeScript',
    readTime: '8 分钟'
  },
  3: {
    id: 3,
    title: 'Tailwind CSS 设计系统构建',
    content: `
      <h2>设计系统的重要性</h2>
      <p>设计系统确保了产品界面的一致性，提高了开发效率，并改善了用户体验。</p>

      <h2>Tailwind CSS 的优势</h2>
      <ul>
        <li><strong>实用优先</strong>：提供低级实用类，构建任何设计</li>
        <li><strong>响应式设计</strong>：内置响应式修饰符</li>
        <li><strong>定制化</strong>：高度可定制的配置系统</li>
        <li><strong>性能优化</strong>：自动清理未使用的样式</li>
      </ul>

      <h2>构建设计系统</h2>
      <ol>
        <li><strong>定义设计令牌</strong>：颜色、字体、间距等</li>
        <li><strong>创建组件库</strong>：可复用的 UI 组件</li>
        <li><strong>建立规范</strong>：使用指南和最佳实践</li>
        <li><strong>工具支持</strong>：开发工具和自动化</li>
      </ol>

      <p>通过系统化的方法，你可以创建一个强大而灵活的设计系统。</p>
    `,
    date: '2024-01-05',
    author: '王五',
    category: 'CSS',
    readTime: '6 分钟'
  }
}

interface BlogPostPageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = blogPosts[parseInt(params.id) as keyof typeof blogPosts]
  
  if (!post) {
    return {
      title: '文章未找到',
    }
  }

  return {
    title: `${post.title} - Next.js App Router 博客`,
    description: post.content.substring(0, 160).replace(/<[^>]*>/g, ''),
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = blogPosts[parseInt(params.id) as keyof typeof blogPosts]

  if (!post) {
    notFound()
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <Link 
          href="/blog"
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          ← 返回博客列表
        </Link>
      </div>

      <article className="prose prose-lg max-w-none">
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
              {post.category}
            </span>
            <span className="text-sm text-gray-500">
              {post.readTime}
            </span>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {post.title}
          </h1>
          
          <div className="flex items-center text-sm text-gray-500 space-x-4">
            <span>作者: {post.author}</span>
            <span>发布于: {post.date}</span>
          </div>
        </div>

        <div 
          className="article-content"
          dangerouslySetInnerHTML={{ __html: post.content }}
        />
      </article>

      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <Link 
            href="/blog"
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            查看更多文章
          </Link>
          
          <div className="text-sm text-gray-500">
            分享到：
            <button className="ml-2 text-blue-600 hover:text-blue-800">Twitter</button>
            <button className="ml-2 text-blue-600 hover:text-blue-800">LinkedIn</button>
          </div>
        </div>
      </div>
    </div>
  )
}

// 生成静态参数
export async function generateStaticParams() {
  return Object.keys(blogPosts).map((id) => ({
    id: id,
  }))
}