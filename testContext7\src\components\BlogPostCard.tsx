import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card'

interface BlogPost {
  id: number
  title: string
  excerpt: string
  date: string
  author: string
  category: string
  readTime: string
}

interface BlogPostCardProps {
  post: BlogPost
}

export function BlogPostCard({ post }: BlogPostCardProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between mb-2">
          <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
            {post.category}
          </span>
          <span className="text-sm text-gray-500">
            {post.readTime}
          </span>
        </div>
        <CardTitle>
          <Link 
            href={`/blog/${post.id}`}
            className="hover:text-blue-600 transition-colors"
          >
            {post.title}
          </Link>
        </CardTitle>
        <CardDescription>
          {post.excerpt}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <span>作者: {post.author}</span>
            <span>发布于: {post.date}</span>
          </div>
          <Link 
            href={`/blog/${post.id}`}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            阅读全文 →
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}