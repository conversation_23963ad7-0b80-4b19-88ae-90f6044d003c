import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="space-y-6">
      <h1 className="text-4xl font-bold text-gray-900">
        欢迎来到 Next.js App Router
      </h1>
      
      <p className="text-xl text-gray-600">
        这是一个基于 Next.js 15 App Router 的现代化项目结构示例。
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-3">关于我们</h2>
          <p className="text-gray-600 mb-4">
            了解我们的团队和使命
          </p>
          <Link 
            href="/about" 
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            查看更多 →
          </Link>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-3">博客</h2>
          <p className="text-gray-600 mb-4">
            阅读最新的技术文章和见解
          </p>
          <Link 
            href="/blog" 
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            访问博客 →
          </Link>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-3">联系我们</h2>
          <p className="text-gray-600 mb-4">
            与我们取得联系，获取更多信息
          </p>
          <Link 
            href="/contact" 
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            联系我们 →
          </Link>
        </div>
      </div>

      <div className="bg-blue-50 p-6 rounded-lg">
        <h2 className="text-2xl font-semibold mb-3">项目特性</h2>
        <ul className="space-y-2 text-gray-700">
          <li>✅ Next.js 15 App Router</li>
          <li>✅ TypeScript 支持</li>
          <li>✅ Tailwind CSS 样式</li>
          <li>✅ 服务端组件优化</li>
          <li>✅ 现代化项目结构</li>
          <li>✅ 自动代码分割</li>
        </ul>
      </div>
    </div>
  )
}